#!/usr/bin/env python3
"""
Test script for Gemini AI-based tag matching integration.
"""

import json
import time

import requests

# Test AWS labels (same as in the Gemini script)
TEST_AWS_LABELS = [
    {
        "score": "0.9999017333984375",
        "label": "Bag"
    },
    {
        "score": "0.9978693389892578",
        "label": "Accessories"
    },
    {
        "score": "0.9978693389892578",
        "label": "Handbag"
    },
    {
        "score": "0.9974147033691406",
        "label": "Clothing"
    },
    {
        "score": "0.9974147033691406",
        "label": "Footwear"
    },
    {
        "score": "0.9974147033691406",
        "label": "Shoe"
    },
    {
        "score": "0.9957437133789062",
        "label": "Backpack"
    },
    {
        "score": "0.9944367218017578",
        "label": "Backpacking"
    },
    {
        "score": "0.9479109954833984",
        "label": "Hat"
    },
    {
        "score": "0.9022752380371094",
        "label": "Photography"
    },
    {
        "score": "0.8934603118896485",
        "label": "Camera"
    },
    {
        "score": "0.8934603118896485",
        "label": "Electronics"
    },
    {
        "score": "0.8562489318847656",
        "label": "Outdoors"
    },
    {
        "score": "0.808559799194336",
        "label": "Adventure"
    },
    {
        "score": "0.808559799194336",
        "label": "Hiking"
    },
    {
        "score": "0.808559799194336",
        "label": "Leisure Activities"
    },
    {
        "score": "0.808559799194336",
        "label": "Nature"
    }
]

def test_gemini_route():
    """Test the new Gemini matching route."""
    print("🧠 Testing Gemini AI Tag Matching Route...")
    
    # Prepare the request payload
    payload = {
        "labels": TEST_AWS_LABELS
    }
    
    # Test the route (tags service runs on port 8012)
    url = "http://localhost:8012/tags/tags/test-match-gemini"
    
    try:
        print(f"📡 Sending request to: {url}")
        print(f"📦 Payload: {len(payload['labels'])} labels")
        
        start_time = time.time()
        response = requests.post(url, json=payload, timeout=30)
        end_time = time.time()
        
        print(f"⏱️  Response time: {end_time - start_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Gemini Route Test Results:")
            print(f"   Matched Tags: {len(result.get('matched', []))}")
            print(f"   Suggested Tags: {len(result.get('suggested', []))}")
            print(f"   Cached: {result.get('cached', False)}")
            
            # Print detailed results
            print("\n📋 Matched Tags:")
            for tag in result.get('matched', []):
                print(f"   - {tag.get('tag')} ({tag.get('category')}) - Score: {tag.get('score', 0):.3f}")
                print(f"     Reasoning: {tag.get('reasoning', 'N/A')}")
            
            print("\n💡 Suggested Tags:")
            for tag in result.get('suggested', []):
                print(f"   - {tag.get('tag')} ({tag.get('category')}) - Score: {tag.get('score', 0):.3f}")
                print(f"     Reasoning: {tag.get('reasoning', 'N/A')}")
            
            if result.get('error'):
                print(f"⚠️  Error: {result.get('error')}")
                
            return True
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the tags service is running on port 8012")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_vector_route_comparison():
    """Test the existing vector route for comparison."""
    print("\n🤖 Testing Vector Tag Matching Route (for comparison)...")
    
    # Prepare the request payload
    payload = {
        "labels": TEST_AWS_LABELS
    }
    
    # Test the route
    url = "http://localhost:8012/tags/tags/test-match"
    
    try:
        print(f"📡 Sending request to: {url}")
        
        start_time = time.time()
        response = requests.post(url, json=payload, timeout=30)
        end_time = time.time()
        
        print(f"⏱️  Response time: {end_time - start_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Vector Route Test Results:")
            print(f"   Matched Tags: {len(result.get('matched', []))}")
            print(f"   Suggested Tags: {len(result.get('suggested', []))}")
            
            # Print detailed results (vector format uses 'parent' instead of 'category')
            print("\n📋 Matched Tags:")
            for tag in result.get('matched', []):
                print(f"   - {tag.get('tag')} ({tag.get('parent')}) - Score: {tag.get('score', 0):.3f}")
            
            print("\n💡 Suggested Tags:")
            for tag in result.get('suggested', []):
                print(f"   - {tag.get('tag')} ({tag.get('parent')}) - Score: {tag.get('score', 0):.3f}")
                
            return True
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed. Make sure the tags service is running on port 8012")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Gemini Integration Tests...\n")
    
    # Test the new Gemini route
    gemini_success = test_gemini_route()
    
    # Test the existing vector route for comparison
    vector_success = test_vector_route_comparison()
    
    print(f"\n📊 Test Summary:")
    print(f"   Gemini Route: {'✅ PASS' if gemini_success else '❌ FAIL'}")
    print(f"   Vector Route: {'✅ PASS' if vector_success else '❌ FAIL'}")
    
    if gemini_success:
        print("\n🎉 Gemini integration is working! You can now test the full task process.")
        print("💡 Next steps:")
        print("   1. Update the API key in the code with your real Gemini API key")
        print("   2. Set USE_GEMINI_MATCHING = True in app/images/tasks.py to enable Gemini matching")
        print("   3. Test the full image processing pipeline")
    else:
        print("\n🔧 Gemini integration needs attention. Check the logs and API key configuration.")
