"""Tags service routes."""


from datetime import UTC, datetime
from typing import Annotated

from anyio import to_thread
from fastapi import APIRouter, Depends, File, Request, UploadFile
from pydantic import BaseModel

from app.core import get_settings
from app.core.auth import TokenData, get_current_user
from app.core.celery_app import celery
from app.core.decorators import response_handler
from app.services.compress_image_service import compress_image_for_rekognition
from app.services.models.tags import (AllMasterTagsResponse, ImageTagUpdate,
                                      UserTagsResponse)
from app.services.simple_image_analysis import (SimpleImageAnalysisService,
                                                get_simple_image_analysis_service)
from app.services.tags_service import TagsService, get_tags_service
from app.tags.match_passion_aws_v5 import (_save_embed_cache, init_matcher,
                                           match_labels_simple)

router = APIRouter()
settings = get_settings()

# Initialize matcher once at startup

@router.on_event("startup")
async def warm_matcher():
    await to_thread.run_sync(init_matcher)

class AWSLabel(BaseModel):
    label: str
    score: float  # accept 0..1 or 0..100

class MatchRequest(BaseModel):
    labels: list[AWSLabel]
    
# Type alias for authenticated user
UserTokenData = Annotated[TokenData, Depends(get_current_user)]


@router.get("/health")
async def health_check() -> dict[str, str]:
    """Health check endpoint for tags service ."""
    return {
        "status": "ok",
        "timestamp": datetime.now(UTC).isoformat(),
        "service": "tags",
        "version": settings.version,
        "environment": settings.environment,
    }


@router.get("/")
async def root() -> dict[str, str]:
    """Root endpoint for tags service."""
    return {
        "message": "Fora-Marriott Tags Service",
        "version": settings.version,
        "docs": "/docs",
        "health": "/health",
    }


@router.get("/protected")
@response_handler
async def protected_tags_endpoint(
    current_user: TokenData = Depends(get_current_user),
) -> dict:
    """Protected endpoint - requires valid JWT token."""
    return {
        "message": "Access granted to Tags service",
        "user_id": current_user.user_id,
        "cust_id": current_user.cust_id,
        "service": "tags",
    }


@router.get("/user/tags")
@response_handler
async def get_user_tags(
    current_user: UserTokenData,
    tags_service: TagsService = Depends(get_tags_service)
) -> dict:
    """Fetch all tags generated for the user's uploaded images."""
    return await tags_service.get_user_tags(str(current_user.user_id))


@router.patch("/user/tags")
@response_handler
async def update_image_tags(
    updates: list[ImageTagUpdate],
    current_user: UserTokenData,
    tags_service: TagsService = Depends(get_tags_service)
) -> dict:
    """
    Update tags for one or more images.

    After successful update, triggers recommendation regeneration.
    """
    user_id = str(current_user.user_id)

    # Update the tags
    result = await tags_service.update_image_tags(user_id, updates)

    # If any images were successfully updated, trigger recommendation regeneration
    if result.get("updated_images", 0) > 0:
        try:
            celery.send_task(
                "images.generate_user_recommendations",
                args=[user_id],
                countdown=2  # Small delay to ensure DB consistency
            )
            result["recommendation_task_triggered"] = True
        except Exception as e:
            # Don't fail the request if recommendation trigger fails
            result["recommendation_task_triggered"] = False
            result["recommendation_task_error"] = str(e)

    return result


@router.get("/user/all/tags")
@response_handler
async def get_all_master_tags(
    current_user: UserTokenData,
    tags_service: TagsService = Depends(get_tags_service)
) -> dict:
    """Fetch all available master tags grouped by parent category."""
    return await tags_service.get_all_master_tags()


@router.delete("/user/profile/reset")
@response_handler
async def reset_user_profile(
    current_user: UserTokenData,
    tags_service: TagsService = Depends(get_tags_service)
) -> dict:
    """Reset user profile by deleting all images and recommendations."""
    return await tags_service.reset_user_profile(str(current_user.user_id))


@router.get("/user/result")
@response_handler
async def get_user_result(
    current_user: UserTokenData,
    tags_service: TagsService = Depends(get_tags_service)
) -> dict:
    """
    Retrieve the final results / personalized passion recommendations.
    """
    return await tags_service.get_user_results(str(current_user.user_id))


# ============================================================================
# IMAGE ANALYSIS ROUTES (LIGHTWEIGHT POC - NO AUTHENTICATION REQUIRED)
# ============================================================================

@router.post("/test/analyze")
@response_handler
async def test_image_analysis(
    file: UploadFile = File(..., description="Image file to analyze"),
    analysis_service: SimpleImageAnalysisService = Depends(get_simple_image_analysis_service)
) -> dict:
    """
    Lightweight POC for image analysis using AWS Rekognition + Gemini AI.

    Open route for testing - no authentication required.
    Upload an image file and get analysis results.

    Supported formats: JPEG, PNG, WebP, GIF
    Max file size: 5MB (AWS Rekognition limit)

    Returns:
    {
        "aws_labels": [...],
        "gemini_result": "...",
        "final_passions": [...]
    }
    """
    try:
        # Validate file type
        if not file.content_type or not file.content_type.startswith('image/'):
            return {
                "error": "Invalid file type. Please upload an image file.",
                "success": False
            }

        # Read upload (you can also stream to a temp file if memory is tight)
        original_bytes = await file.read()
        
        # Compress/convert for Rekognition (to JPEG <= 5MB)
        # Offload CPU work to a thread (smooth event loop)
        comp = await to_thread.run_sync(compress_image_for_rekognition, original_bytes)
        processed_bytes = comp["processed_bytes"]
        processed_ct = comp["content_type"]
        metrics = comp["metrics"]
        
        # Read file content
        # image_bytes = await file.read()

        # Check file size (5MB limit for AWS Rekognition)
        if len(processed_bytes) > 5 * 1024 * 1024:  # 5MB
            return {
                "error": "File too large. Maximum size is 5MB.",
                "success": False,
                "metrics": metrics,  # helps you see why it failed
            }

        # Analyze the image
        result = await analysis_service.analyze_image(
            image_bytes=processed_bytes,
            image_name=file.filename or "uploaded_image"
        )
          # Optionally include compression metrics in the response for observability
        # (Or log them internally instead)
        result["compression_metrics"] = metrics
        
        return result

    except Exception as e:
        return {
            "error": f"Upload error: {str(e)}",
            "success": False
        }
        
@router.post("/tags/test-match")
async def test_match(payload: MatchRequest):
    """
    Simple route to test AWS Rekognition label matching.
    Send a JSON body with only labels (no other params).
    """
    # convert payload to plain list of dicts
    labels = [l.model_dump() for l in payload.labels]
    result = await to_thread.run_sync(match_labels_simple, labels)
    # persist token cache so the file appears
    await to_thread.run_sync(_save_embed_cache)
    return result