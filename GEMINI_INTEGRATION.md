# Gemini AI Integration for Tag Matching

This document describes the integration of Google Gemini AI for passion tag matching in the Fora-Marriott backend.

## Overview

The system now supports three tag matching methods:
1. **Manual Mapping** (legacy) - Uses hardcoded label-to-tag mappings
2. **Vector Matching** (current default) - Uses AI embeddings and cosine similarity
3. **Gemini AI Matching** (new) - Uses Google Gemini AI for semantic understanding

## Configuration

The matching method is controlled by flags in `app/images/tasks.py`:

```python
# Hardcoded configuration: Choose matching method
USE_VECTOR_MATCHING = True   # Set to False to use manual mapping
USE_GEMINI_MATCHING = False  # Set to True to use Gemini AI matching
```

**Priority Order:**
1. If `USE_GEMINI_MATCHING = True` → Uses Gemini AI
2. Else if `USE_VECTOR_MATCHING = True` → Uses vector matching
3. Else → Uses manual mapping

## Gemini AI Integration

### Files Modified/Added:
- `app/images/tasks.py` - Added `_match_tags_gemini()` function
- `app/tags/routes.py` - Added `/tags/test-match-gemini` test route
- `app/tags/match_passion_google_v1.py` - Gemini matching implementation
- `test_gemini_integration.py` - Test script for the integration

### Key Features:
- **Semantic Understanding**: Uses natural language processing to understand context
- **Caching**: Built-in LRU cache with TTL for performance
- **Retry Logic**: Automatic retries with exponential backoff
- **Error Handling**: Graceful fallback to empty results

### Response Format:
The Gemini matcher returns results in this format:
```json
{
  "matched": [
    {
      "tag": "Hike & Glamp",
      "category": "OUTDOOR",
      "score": 0.85,
      "reasoning": "Strong signals from 'Backpack', 'Backpacking', 'Outdoors', 'Adventure', and 'Hiking'."
    }
  ],
  "suggested": [
    {
      "tag": "Art & Photography",
      "category": "ARTS & CULTURE", 
      "score": 0.72,
      "reasoning": "Camera and Photography labels indicate interest in capturing moments."
    }
  ],
  "cached": false,
  "error": null
}
```

**Note**: The Gemini response uses `"category"` while the vector matching uses `"parent"`. The integration handles this mapping automatically.

## Setup Instructions

### 1. Install Dependencies
```bash
pip install google-generativeai
```

### 2. Configure API Key
Update the API key in both locations:

**In `app/images/tasks.py`:**
```python
GEMINI_API_KEY = "YOUR_ACTUAL_GEMINI_API_KEY_HERE"
```

**In `app/tags/routes.py`:**
```python
GEMINI_API_KEY = "YOUR_ACTUAL_GEMINI_API_KEY_HERE"
```

### 3. Enable Gemini Matching
Set the flag in `app/images/tasks.py`:
```python
USE_GEMINI_MATCHING = True
```

## Testing

### Test the Route Directly:
```bash
python test_gemini_integration.py
```

This will test both the new Gemini route and compare it with the existing vector route.

### Test via API:
```bash
curl -X POST "http://localhost:8003/tags/test-match-gemini" \
  -H "Content-Type: application/json" \
  -d '{
    "labels": [
      {"label": "Backpack", "score": "0.99"},
      {"label": "Hiking", "score": "0.85"},
      {"label": "Outdoors", "score": "0.80"}
    ]
  }'
```

### Test Full Pipeline:
Once the route test passes, you can test the full image processing pipeline by uploading images through the normal API endpoints.

## Configuration Parameters

### Gemini Matching Parameters:
```python
GEMINI_TOP_K = 3                    # Max matched tags to return
GEMINI_API_KEY = "your-key-here"    # Google Gemini API key
GEMINI_MODEL = "models/gemini-2.0-flash"  # Model to use
```

### Gemini Model Configuration:
```python
temperature = 0.1          # Lower = more deterministic
top_p = 0.8               # Nucleus sampling parameter
max_output_tokens = 1024  # Max response length
cache_capacity = 2000     # Number of cached responses
cache_ttl_seconds = 3600  # Cache expiration time
max_retries = 4           # Retry attempts on failure
timeout_seconds = 20      # Request timeout
```

## Advantages of Gemini AI Matching

1. **Better Context Understanding**: Can understand complex relationships between labels
2. **Reasoning**: Provides explanations for matches
3. **Flexibility**: No need to maintain embedding vectors or manual mappings
4. **Accuracy**: Better handling of edge cases and ambiguous labels
5. **Scalability**: Easy to add new passions without retraining

## Performance Considerations

- **First Request**: May be slower due to model initialization
- **Cached Requests**: Very fast for repeated label combinations
- **API Limits**: Subject to Google Gemini API rate limits and quotas
- **Cost**: API usage costs apply (check Google pricing)

## Monitoring

The integration includes comprehensive logging:
- Request/response times
- Cache hit/miss rates
- Error rates and retry attempts
- Match quality metrics

Check logs for entries like:
- `🧠 Using Gemini AI-based tag matching`
- `🔧 Initializing Gemini matcher`
- `🎯 Running Gemini AI matching`
- `✅ Gemini matching completed`

## Troubleshooting

### Common Issues:

1. **Import Error**: Make sure `google-generativeai` is installed
2. **API Key Error**: Verify the API key is correct and has proper permissions
3. **Rate Limiting**: Implement backoff or reduce request frequency
4. **Model Not Found**: Check if the model name is correct and available

### Debug Steps:
1. Run `test_gemini_integration.py` to test the route
2. Check logs for detailed error messages
3. Verify API key permissions in Google Cloud Console
4. Test with a simple label set first
