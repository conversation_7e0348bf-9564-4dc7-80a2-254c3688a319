[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[project]
name = "marriott-pseudo-microservice"
version = "1.0.0"
description = "Marriott pseudo-microservice architecture with FastAP<PERSON>, uv, ruff, and mypy"
authors = [
    {name = "Marriott Team", email = "<EMAIL>"},
]
# readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    "fastapi[standard]>=0.115.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.0.0",
    "python-dotenv>=1.0.0",
    "boto3>=1.34.0",
    "python-multipart>=0.0.6",
    "sentry-sdk[fastapi]>=1.40.0",
    "celery[redis]>=5.5.3",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "flower>=2.0.1",
    "redis>=5.2.1",
    "google-genai>=1.45.0",
    "pillow>=12.0.0",
    "pillow-heif>=1.1.1",
    "numpy>=2.3.4",
    "google-generativeai>=0.8.5",
]

[project.optional-dependencies]
dev = [
    "ruff>=0.8.0",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0",
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "httpx>=0.28.0",
    "coverage>=7.4.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/fastapi-microservice-boilerplate"
Repository = "https://github.com/yourusername/fastapi-microservice-boilerplate"
Issues = "https://github.com/yourusername/fastapi-microservice-boilerplate/issues"

[dependency-groups]
dev = [
    "coverage>=7.10.6",
    "httpx>=0.28.1",
    "mypy>=1.17.1",
    "pre-commit>=4.3.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "ruff>=0.12.11",
]

[tool.ruff]
target-version = "py311"
line-length = 88

[tool.ruff.lint]
# Lightweight, industry-standard rules
select = [
    "E",   # pycodestyle errors (PEP 8)
    "W",   # pycodestyle warnings (PEP 8)
    "F",   # pyflakes (undefined names, imports)
    "I",   # isort (import sorting)
    "B",   # flake8-bugbear (common bugs)
    "UP",  # pyupgrade (modern Python syntax)
    "C4",  # flake8-comprehensions (list/dict comprehensions)
]

# Minimal ignores - only ignore what's truly necessary
ignore = [
    "E501",  # Line too long (handled by formatter)
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.ruff.lint.isort]
known-first-party = ["app"]

[tool.ruff.lint.per-file-ignores]
"tests/**/*" = ["B"]  # Allow assert statements in tests

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true
strict_concatenate = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src"]
omit = ["tests/*", "scripts/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
