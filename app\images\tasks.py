# app/images/tasks.py
"""
Celery tasks for image processing pipeline.

DynamoDB Schema Fields Expected:
BATCH:
- processing_started_at: ISO timestamp when processing began
- processing_completed_at: ISO timestamp when processing completed
- progress: {"processed": int, "total": int}

IMAGE:
- processing_state: "PENDING" | "PROCESSING" | "DONE" | "FAILED"
- processing_started_at: ISO timestamp when processing began
- processing_completed_at: ISO timestamp when processing completed
- rekognition_request_id: AWS Rekognition request ID
- retries: int count of retry attempts
- last_error: str error message from last failure
- aws_labels: list of AWS Rekognition labels with scores
- matched_tags: list of matched Marriott tags

Worker Configuration:
For workers handling Rekognition-heavy queues, recommend:
worker_prefetch_multiplier=1 to prevent memory issues with large images
"""

import asyncio
import json
import logging
import math
import re
import time
import traceback
import unicodedata
import uuid
from datetime import UTC, datetime
from decimal import Decimal
from typing import Any, Callable

import boto3
import botocore
from boto3.dynamodb.conditions import Key
from boto3.dynamodb.types import TypeDeserializer, TypeSerializer
from botocore.exceptions import ClientError
from celery import chain, chord, group

from app.core.celery_app import celery
from app.core.enums import BatchStatus, ProcessingState

# Configure logging
logger = logging.getLogger(__name__)

# Module-level boto3 clients (thread-safe)
rekognition_client = None
s3_client = None


def get_boto3_clients():
    """Initialize boto3 clients lazily."""
    global rekognition_client, s3_client

    if rekognition_client is None:
        from app.core.config import get_settings

        settings = get_settings()
        
        # Initialize S3 and Rekognition clients using default credentials IAM role
        # rekognition_client = boto3.client("rekognition", region_name=settings.aws_region)
        # s3_client = boto3.client("s3", region_name=settings.aws_region)


        # Common AWS config for S3 and Rekognition (same keys)
        
        aws_config = {
            "region_name": settings.aws_region,
        }
        
        if settings.s3_access_key_id and settings.s3_secret_access_key:
            aws_config["aws_access_key_id"] = settings.s3_access_key_id
            aws_config["aws_secret_access_key"] = settings.s3_secret_access_key
        # Initialize S3 and Rekognition clients
        
        rekognition_client = boto3.client("rekognition", **aws_config)
        s3_client = boto3.client("s3", **aws_config)

    return rekognition_client, s3_client


async def async_boto3_call(fn: Callable[..., Any], *args, **kwargs) -> Any:
    """
    Helper function to call boto3 methods asynchronously using asyncio.to_thread.

    Args:
        fn: The boto3 method to call
        *args: Positional arguments for the method
        **kwargs: Keyword arguments for the method

    Returns:
        The result of the boto3 method call
    """
    return await asyncio.to_thread(fn, *args, **kwargs)


# We'll use the existing database service directly instead of importing separate service functions


# Task configurations
TASK_CONFIG = {
    "acks_late": False,
    "task_acks_on_failure_or_timeout" : False,
    "max_retries": 3,
    "default_retry_delay": 60,  # 1 minute
    "retry_backoff": True,
    "retry_backoff_max": 600,  # 10 minutes
    "retry_jitter": True,
    "worker_prefetch_multiplier":1,  # Prevent memory issues with large images
}

# Normalize options
USE_UNDERSCORE = True  # True -> use '_' as separator, False -> use '-'
KEEP_AMPERSAND = False  # False -> replace '&' with '_and_'; True -> keep '&'


@celery.task(name="images.process_batch", **TASK_CONFIG)
def process_batch_task(user_id: str, batch_id: str) -> dict:
    """
    Main orchestration task for processing an image batch.

    1. Marks BATCH.status=PROCESSING and sets processing_started_at
    2. Loads batch.image_refs and fans out per-image tasks
    3. Uses chord(group(...)) to aggregate results when all images are done

    Args:
        user_id: User ID
        batch_id: Batch ID to process

    Returns:
        dict: Task result with batch info
    """
    start_time = time.perf_counter()
    logger.info(f"Starting batch processing: user_id={user_id}, batch_id={batch_id}")

    try:
        # Get database service
        from app.core.database import get_db_service

        db = get_db_service()

        # Update batch status to PROCESSING
        now = datetime.now(UTC).isoformat()
        db.table.update_item(
            Key={"PK": f"USER#{user_id}", "SK": f"BATCH#{batch_id}"},
            UpdateExpression="SET #status = :status, processing_started_at = :started_at",
            ExpressionAttributeNames={"#status": "status"},
            ExpressionAttributeValues={
                ":status": BatchStatus.PROCESSING.value,
                ":started_at": now,
            },
        )

        # Get batch details to extract image_refs
        response = db.table.get_item(
            Key={"PK": f"USER#{user_id}", "SK": f"BATCH#{batch_id}"}
        )

        if "Item" not in response:
            raise ValueError(f"Batch not found: {batch_id}")

        batch_item = response["Item"]
        image_refs = batch_item.get("image_refs", [])

        if not image_refs:
            logger.warning(f"No images found in batch {batch_id}")
            return {"status": "completed", "message": "No images to process"}

        # Create group of image processing tasks
        image_tasks = group(
            [
                process_image_task.s(user_id, batch_id, ref["image_id"], ref["s3_key"])
                for ref in image_refs
            ]
        )

        # Use group to run all image tasks in parallel (no chord needed since batch completion is handled in match_tags_task)
        workflow = group(image_tasks)

        # Execute the group
        workflow_result = workflow.apply_async()

        duration = time.perf_counter() - start_time
        logger.info(
            f"Batch processing orchestrated: batch_id={batch_id}, image_count={len(image_refs)}, duration={duration:.2f}s"
        )

        return {
            "status": "orchestrated",
            "batch_id": batch_id,
            "image_count": len(image_refs),
            "workflow_id": workflow_result.id if workflow_result else None,
            "duration_seconds": duration,
        }

    except Exception as e:
        error_msg = f"Failed to process batch {batch_id}: {str(e)}"
        logger.error(error_msg, exc_info=True)

        # Update batch status to FAILED
        try:
            db.table.update_item(
                Key={"PK": f"USER#{user_id}", "SK": f"BATCH#{batch_id}"},
                UpdateExpression="SET #status = :status, last_error = :error",
                ExpressionAttributeNames={"#status": "status"},
                ExpressionAttributeValues={
                    ":status": BatchStatus.FAILED.value,
                    ":error": error_msg,
                },
            )
        except Exception as update_error:
            logger.error(f"Failed to update batch status: {update_error}")

        raise


@celery.task(name="images.process_image", **TASK_CONFIG)
def process_image_task(user_id: str, batch_id: str, image_id: str, s3_key: str) -> dict:
    """
    Process a single image with AWS Rekognition.

    1. Check if already processed (idempotent)
    2. Call AWS Rekognition detect_labels
    3. Save aws_labels and update IMAGE processing state
    4. Chain to match_tags_task

    Args:
        user_id: User ID
        batch_id: Batch ID
        image_id: Image ID to process
        s3_key: S3 key for the image

    Returns:
        dict: Task result with image processing info
    """
    start_time = time.perf_counter()
    logger.info(
        f"Processing image: user_id={user_id}, image_id={image_id}, s3_key={s3_key}"
    )

    try:
        # Get database service
        from app.core.config import get_settings
        from app.core.database import get_db_service

        db = get_db_service()
        settings = get_settings()

        # Check if already processed (idempotency)
        response = db.table.get_item(
            Key={"PK": f"USER#{user_id}", "SK": f"IMAGE#{image_id}"}
        )

        if "Item" in response:
            item = response["Item"]
            if item.get("processing_state") == ProcessingState.DONE.value or item.get("aws_labels"):
                logger.info(f"Image {image_id} already processed, skipping")
                return {
                    "status": "skipped",
                    "image_id": image_id,
                    "reason": "already_processed",
                }

        # Update IMAGE processing state to PROCESSING
        now = datetime.now(UTC).isoformat()
        db.table.put_item(
            Item={
                "PK": f"USER#{user_id}",
                "SK": f"IMAGE#{image_id}",
                "image_id": image_id,
                "s3_key": s3_key,
                "batch_id": batch_id,
                "processing_state": ProcessingState.PROCESSING.value,
                "processing_started_at": now,
                "created_at": now,
                "retries": 0,
            }
        )

        # Get boto3 clients
        rekognition_client, s3_client = get_boto3_clients()
        original_labels_to_exclude = [
                                "Adult", "Alien", "Angel", "Angler", "Ankle", "Archer", "Arm", "Baby", "Back", "Barefoot", "Beard",
                                "Body Part", "Boy", "Bride", "Bridegroom", "Bridesmaid", "Bullfighter", "Child", "Crowd", "Cupid",
                                "Dimples", "Ear", "Elf", "Face", "Family", "Female", "Finger", "Fist", "Freckle", "Girl", "Hairdresser",
                                "Hand", "Head", "Heart", "Heel", "Hip", "Hippie", "Jaw", "Jury", "Knee", "Lady", "Male", "Man",
                                "Military Officer", "Monk", "Mouth", "Mustache", "Navel", "Neck", "Newborn", "Pedestrian", "People",
                                "Person", "Senior Citizen", "Shoulder", "Skeleton", "Skin", "Stomach", "Student", "Team", "Teen",
                                "Teeth", "Thigh", "Throat", "Toe", "Tongue", "Torso", "Tourist", "Tribe", "Troop", "Veins", "Woman", "Wrist"
                            ]

        include_labels = [
                "Baby", "Boy", "Child", "Family", "Hairdresser", "Girl", "Jury", "Monk",
                "Senior Citizen", "Student", "Team", "Teen"
            ]

        aws_rek_exclude_label_list = [label for label in original_labels_to_exclude if label not in include_labels]

        # Call AWS Rekognition detect_labels using asyncio.to_thread
        async def detect_labels_async():            
            return await async_boto3_call(
                rekognition_client.detect_labels,
                Image={
                    "S3Object": {
                        "Bucket": settings.s3_bucket_name,
                        "Name": s3_key,
                    }
                },
                MaxLabels=30,
                MinConfidence=float(settings.rekognition_confidence_threshold or 70.0),
                Settings={
                "GeneralLabels": {
                    "LabelCategoryExclusionFilters": [
                        "Damage Detection",
                        "Everyday Objects",
                        "Home Appliances",
                        "Patterns and Shapes",
                        "Materials",
                        # "Offices and Workspaces",
                        # "Person Description",
                        "Furniture and Furnishings",
                        "Public Safety",
                        "Religion",
                        "Tools and Machinery",
                        "Weapons and Military",
                    ],
                    "LabelExclusionFilters":aws_rek_exclude_label_list
                }
            },
            )

        # Run the async function
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            rekognition_response = loop.run_until_complete(detect_labels_async())
        finally:
            loop.close()

        # Extract labels (convert floats to Decimal for DynamoDB)
        # Sort by confidence and take top 5 only
        all_labels = rekognition_response.get("Labels", [])
        top_labels = sorted(all_labels, key=lambda x: x["Confidence"], reverse=True)[:30]

        aws_labels = [
            {
                "label": label["Name"],
                "score": Decimal(
                    str(label["Confidence"] / 100.0)
                ),  # Convert to 0-1 scale as Decimal
            }
            for label in top_labels
        ]

        rekognition_request_id = rekognition_response.get("ResponseMetadata", {}).get(
            "RequestId", ""
        )

        # Update IMAGE with results
        completed_at = datetime.now(UTC).isoformat()
        db.table.update_item(
            Key={"PK": f"USER#{user_id}", "SK": f"IMAGE#{image_id}"},
            UpdateExpression="""
                SET aws_labels = :labels,
                    processing_state = :state,
                    processing_completed_at = :completed_at,
                    rekognition_request_id = :req_id
            """,
            ExpressionAttributeValues={
                ":labels": aws_labels,
                ":state": ProcessingState.DONE.value,
                ":completed_at": completed_at,
                ":req_id": rekognition_request_id,
            },
        )

        # Update batch progress
        try:
            db.table.update_item(
                Key={"PK": f"USER#{user_id}", "SK": f"BATCH#{batch_id}"},
                UpdateExpression="ADD progress.#processed :inc",
                ExpressionAttributeNames={"#processed": "processed"},
                ExpressionAttributeValues={":inc": 1},
            )
        except Exception as progress_error:
            logger.warning(f"Failed to update batch progress: {progress_error}")

        # Chain to match_tags_task (this will complete the batch processing)
        match_tags_task.apply_async(
            args=(user_id, batch_id, image_id, aws_labels),
            countdown=1,  # Small delay to ensure DB consistency
        )

        duration = time.perf_counter() - start_time
        logger.info(
            f"Image processed successfully: image_id={image_id}, labels_count={len(aws_labels)}, duration={duration:.2f}s"
        )

        return {
            "status": "completed",
            "image_id": image_id,
            "labels_count": len(aws_labels),
            "rekognition_request_id": rekognition_request_id,
            "duration_seconds": duration,
        }

    except Exception as e:
        error_msg = f"Failed to process image {image_id}: {str(e)}"
        error_trace = traceback.format_exc()
        logger.error(f"{error_msg}\n{error_trace}")

        # Update IMAGE with error info
        try:
            db.table.update_item(
                Key={"PK": f"USER#{user_id}", "SK": f"IMAGE#{image_id}"},
                UpdateExpression="""
                    SET processing_state = :state,
                        last_error = :error,
                        retries = if_not_exists(retries, :zero) + :one
                """,
                ExpressionAttributeValues={
                    ":state": ProcessingState.FAILED.value,
                    ":error": error_msg,
                    ":zero": 0,
                    ":one": 1,
                },
            )
        except Exception as update_error:
            logger.error(f"Failed to update image error state: {update_error}")

        # Retry logic
        if process_image_task.request.retries < process_image_task.max_retries:
            logger.info(
                f"Retrying image processing: image_id={image_id}, retry={process_image_task.request.retries + 1}"
            )
            raise process_image_task.retry(
                countdown=60 * (2**process_image_task.request.retries)
            )  # Exponential backoff

        raise


@celery.task(name="images.match_tags", **TASK_CONFIG)
def match_tags_task(
    user_id: str, batch_id: str, image_id: str, aws_labels: list
) -> dict:
    """
    Match AWS Rekognition labels with Marriott tags.

    Supports both manual mapping (legacy) and vector-based matching (new).
    The method is controlled by the use_vector_tag_matching setting.

    Vector matching:
      - Uses AI embeddings and cosine similarity for semantic matching
      - Returns matched and suggested tags with confidence scores

    Manual mapping (legacy):
      - Uses predefined AWS label -> Marriott tag mappings in DynamoDB
      - Aggregates candidates and splits into matched/suggested
    """
    start_time = time.perf_counter()
    logger.info(
        f"Matching tags for image: user_id={user_id}, batch_id={batch_id}, image_id={image_id}, labels_count={len(aws_labels)}"
    )

    try:
        from app.core.database import get_db_service

        db = get_db_service()

        # Hardcoded configuration: Choose matching method
        USE_VECTOR_MATCHING = True  # Set to False to use manual mapping
        USE_GEMINI_MATCHING = False  # Set to True to use Gemini AI matching

        if USE_GEMINI_MATCHING:
            logger.info(f"🧠 Using Gemini AI-based tag matching for image_id={image_id}")
            return _match_tags_gemini(user_id, batch_id, image_id, aws_labels, db, start_time)
        elif USE_VECTOR_MATCHING:
            logger.info(f"🤖 Using vector-based tag matching for image_id={image_id}")
            return _match_tags_vector(user_id, batch_id, image_id, aws_labels, db, start_time)
        else:
            logger.info(f"📋 Using manual mapping for image_id={image_id}")
            return _match_tags_manual(user_id, batch_id, image_id, aws_labels, db, start_time)

    except Exception as e:
        error_msg = f"Failed to match tags for image {image_id}: {str(e)}"
        logger.error(error_msg, exc_info=True)

        # Update IMAGE with error info (best-effort)
        try:
            from app.core.database import get_db_service
            db = get_db_service()
            db.table.update_item(
                Key={"PK": f"USER#{user_id}", "SK": f"IMAGE#{image_id}"},
                UpdateExpression="SET last_error = :error",
                ExpressionAttributeValues={":error": error_msg},
            )
        except Exception as update_error:
            logger.error(f"Failed to update image error state: {update_error}")

        # Retry logic
        if match_tags_task.request.retries < match_tags_task.max_retries:
            logger.info(
                f"Retrying tag matching: image_id={image_id}, retry={match_tags_task.request.retries + 1}"
            )
            raise match_tags_task.retry(
                countdown=30 * (2 ** match_tags_task.request.retries)
            )

        raise


def _match_tags_vector(
    user_id: str, batch_id: str, image_id: str, aws_labels: list, db, start_time: float
) -> dict:
    """Handle vector-based tag matching using direct script call."""
    try:
        # Hardcoded vector matching parameters (no env variables)
        VECTOR_TOP_K = 3
        VECTOR_MIN_SCORE = 0.40
        VECTOR_PER_PARENT_CAP = 2
        VECTOR_SUGGEST_K = 2
        VECTOR_SUGGEST_MIN_SCORE = 0.30
        VECTOR_SUGGEST_PER_PARENT_CAP = 1

        # Import and initialize the vector matching script
        from app.tags.match_passion_aws_v5 import (_save_embed_cache, init_matcher,
                                                   match_labels_simple)

        # Initialize the matcher (this loads/creates embeddings)
        logger.info(f"🔧 Initializing vector matcher for image_id={image_id}")
        init_matcher()

        # Call the vector matching function directly
        logger.info(f"🎯 Running vector matching for {len(aws_labels)} labels")
        result = match_labels_simple(
            aws_labels_scored=aws_labels,
            top_k=VECTOR_TOP_K,
            min_report_score=VECTOR_MIN_SCORE,
            per_parent_cap=VECTOR_PER_PARENT_CAP,
            suggest_k=VECTOR_SUGGEST_K,
            suggest_min_score=VECTOR_SUGGEST_MIN_SCORE,
            suggest_per_parent_cap=VECTOR_SUGGEST_PER_PARENT_CAP
        )

        # Save embedding cache after matching
        _save_embed_cache()

        # Transform results to match existing API format
        matched_tags = []
        suggested_tags = []

        for item in result.get("matched", []):
            matched_tags.append({
                "marriott_tag": item["tag"],
                "parent_cat": item["parent"],
                "aws_label_name": "",  # Empty as requested
                "score": Decimal(str(item.get("score", "0.0")))

            })

        for item in result.get("suggested", []):
            suggested_tags.append({
                "marriott_tag": item["tag"],
                "parent_cat": item["parent"],
                "aws_label_name": "",  # Empty as requested
                "score": Decimal(str(item.get("score", "0.0")))

            })

        # Update the image with matched and suggested tags
        db.table.update_item(
            Key={"PK": f"USER#{user_id}", "SK": f"IMAGE#{image_id}"},
            UpdateExpression="SET matched_tags = :matched, suggested_tags = :suggested",
            ExpressionAttributeValues={
                ":matched": matched_tags,
                ":suggested": suggested_tags,
            },
        )

        logger.info(
            f"✅ Vector matching saved: {len(matched_tags)} matched, {len(suggested_tags)} suggested tags for image_id={image_id}"
        )

        # Handle batch progress
        _handle_batch_progress_sync(user_id, batch_id, db)

        duration = time.perf_counter() - start_time
        logger.info(
            f"Vector tag matching completed: image_id={image_id}, matched={len(matched_tags)}, suggested={len(suggested_tags)}, duration={duration:.2f}s"
        )

        return {
            "status": "completed",
            "method": "vector",
            "image_id": image_id,
            "batch_id": batch_id,
            "aws_labels_count": len(aws_labels or []),
            "matched_tags_count": len(matched_tags),
            "suggested_tags_count": len(suggested_tags),
            "duration_seconds": duration,
            "_diagnostics": result.get("_diagnostics", {})
        }

    except Exception as e:
        logger.error(f"❌ Vector tag matching failed for image_id={image_id}: {e}", exc_info=True)
        # Fall back to empty results to maintain API compatibility
        db.table.update_item(
            Key={"PK": f"USER#{user_id}", "SK": f"IMAGE#{image_id}"},
            UpdateExpression="SET matched_tags = :matched, suggested_tags = :suggested",
            ExpressionAttributeValues={":matched": [], ":suggested": []},
        )
        raise


def _match_tags_gemini(
    user_id: str, batch_id: str, image_id: str, aws_labels: list, db, start_time: float
) -> dict:
    """Handle Gemini AI-based tag matching using the Google Gemini API."""
    try:
        # Hardcoded Gemini matching parameters
        GEMINI_TOP_K = 3
        GEMINI_API_KEY = "YOUR_GEMINI_API_KEY_HERE"  # TODO: Replace with actual Gemini API key
        GEMINI_MODEL = "models/gemini-2.0-flash"

        # Import and initialize the Gemini matching script
        from app.tags.match_passion_google_v1 import LLMPassionMatcher, MatcherConfig

        # Initialize the matcher with configuration
        logger.info(f"🔧 Initializing Gemini matcher for image_id={image_id}")
        config = MatcherConfig(
            api_key=GEMINI_API_KEY,
            model_name=GEMINI_MODEL,
            temperature=0.1,
            top_p=0.8,
            max_output_tokens=1024,
            cache_capacity=2000,
            cache_ttl_seconds=3600,
            max_retries=4,
            timeout_seconds=20
        )
        matcher = LLMPassionMatcher(config)

        # Call the Gemini matching function
        logger.info(f"🎯 Running Gemini AI matching for {len(aws_labels)} labels")
        result = matcher.match(aws_labels, top_k=GEMINI_TOP_K)

        # Transform the result format to match expected structure
        # Change "parent" to "category" and ensure proper format
        matched_tags = []
        suggested_tags = []

        for item in result.get("matched", []):
            matched_tags.append({
                "tag": item.get("tag"),
                "category": item.get("category"),  # Already using "category" from Gemini response
                "score": item.get("score", 0.0),
                "reasoning": item.get("reasoning", "")
            })

        for item in result.get("suggested", []):
            suggested_tags.append({
                "tag": item.get("tag"),
                "category": item.get("category"),  # Already using "category" from Gemini response
                "score": item.get("score", 0.0),
                "reasoning": item.get("reasoning", "")
            })

        # Update the database with results
        duration = time.perf_counter() - start_time
        logger.info(
            f"✅ Gemini matching completed for image_id={image_id}: "
            f"{len(matched_tags)} matched, {len(suggested_tags)} suggested, "
            f"duration={duration:.2f}s"
        )

        # Store results in database (same format as vector matching)
        db.table.update_item(
            Key={"PK": f"USER#{user_id}", "SK": f"IMAGE#{image_id}"},
            UpdateExpression="SET matched_tags = :matched, suggested_tags = :suggested",
            ExpressionAttributeValues={
                ":matched": [{"tag": t["tag"], "parent": t["category"]} for t in matched_tags],
                ":suggested": [{"tag": t["tag"], "parent": t["category"]} for t in suggested_tags],
            },
        )

        # Handle batch progress
        _handle_batch_progress_sync(user_id, batch_id, db)

        return {
            "status": "completed",
            "method": "gemini",
            "image_id": image_id,
            "batch_id": batch_id,
            "aws_labels_count": len(aws_labels or []),
            "matched_tags_count": len(matched_tags),
            "suggested_tags_count": len(suggested_tags),
            "duration_seconds": duration,
            "_diagnostics": {
                "cached": result.get("cached", False),
                "error": result.get("error"),
                "model": GEMINI_MODEL,
                "top_k": GEMINI_TOP_K
            }
        }

    except Exception as e:
        logger.error(f"❌ Gemini tag matching failed for image_id={image_id}: {e}", exc_info=True)
        # Fall back to empty results to maintain API compatibility
        db.table.update_item(
            Key={"PK": f"USER#{user_id}", "SK": f"IMAGE#{image_id}"},
            UpdateExpression="SET matched_tags = :matched, suggested_tags = :suggested",
            ExpressionAttributeValues={":matched": [], ":suggested": []},
        )
        raise


def _handle_batch_progress_sync(user_id: str, batch_id: str, db) -> None:
    """Handle batch progress tracking synchronously."""
    try:
        from datetime import UTC, datetime

        from app.core.enums import BatchStatus
        from app.images.tasks_old import generate_user_recommendations_task

        batch_response = db.table.get_item(
            Key={"PK": f"USER#{user_id}", "SK": f"BATCH#{batch_id}"}
        )

        if "Item" in batch_response:
            batch = batch_response["Item"]
            progress = batch.get("progress", {})
            processed = int(progress.get("processed", 0)) + 1
            total = int(progress.get("total", 1))

            db.table.update_item(
                Key={"PK": f"USER#{user_id}", "SK": f"BATCH#{batch_id}"},
                UpdateExpression="SET progress.#processed = :processed",
                ExpressionAttributeNames={"#processed": "processed"},
                ExpressionAttributeValues={":processed": processed},
            )

            if processed >= total:
                completed_at = datetime.now(UTC).isoformat()
                db.table.update_item(
                    Key={"PK": f"USER#{user_id}", "SK": f"BATCH#{batch_id}"},
                    UpdateExpression="""
                        SET #status = :status,
                            processing_completed_at = :completed_at
                    """,
                    ExpressionAttributeNames={"#status": "status"},
                    ExpressionAttributeValues={
                        ":status": BatchStatus.COMPLETED.value,
                        ":completed_at": completed_at,
                    },
                )
                logger.info(
                    f"Batch processing completed: batch_id={batch_id}, total_images={total}"
                )

                # trigger recommendations
                try:
                    generate_user_recommendations_task.apply_async(
                        args=[user_id],
                        countdown=2
                    )
                    logger.info(f"Triggered user recommendations generation for user_id={user_id}")
                except Exception as rec_error:
                    logger.warning(f"Failed to trigger recommendations: {rec_error}")

    except Exception as batch_error:
        logger.warning(f"Failed to update batch completion status: {batch_error}")


def _match_tags_manual(
    user_id: str, batch_id: str, image_id: str, aws_labels: list, db, start_time: float
) -> dict:
    """Handle manual/legacy tag matching using DynamoDB mappings."""

    # 1) Aggregate candidate tags (unique by marriott_tag + parent_cat) with max score
    # key: (marriott_tag, parent_cat) -> {"marriott_tag": ..., "parent_cat": ..., "score": max_score}
    candidate_map = {}

    for aws_label in aws_labels:
            label_name = aws_label.get("label", "")
            label_score = Decimal(aws_label.get("score", 0.0))

            normalized_label = normalize_token(label_name)

            try:
                response = db.table.query(
                    KeyConditionExpression="PK = :pk",
                    ExpressionAttributeValues={":pk": f"AWS#{normalized_label}"},
                )
            except Exception as lookup_error:
                logger.warning(
                    f"Failed to lookup AWS label '{label_name}': {lookup_error}"
                )
                continue

            for item in response.get("Items", []):
                marriott_tag = item.get("marriott_tag")
                parent_cat = item.get("parent_cat")

                if not marriott_tag:
                    continue

                key = (marriott_tag, parent_cat)
                existing = candidate_map.get(key)
                # keep the maximum score seen for this marriott_tag
                if existing is None or label_score > existing["score"]:
                    candidate_map[key] = {
                        "marriott_tag": marriott_tag,
                        "parent_cat": parent_cat,
                        "score": label_score,
                        "label_name": label_name,
                    }

    # If no candidates found, write empty lists and continue (so clients see these keys)
    if not candidate_map:
        db.table.update_item(
            Key={"PK": f"USER#{user_id}", "SK": f"IMAGE#{image_id}"},
            UpdateExpression="SET matched_tags = :matched, suggested_tags = :suggested",
            ExpressionAttributeValues={":matched": [], ":suggested": []},
        )
        logger.info(
            f"No tag candidates found for image_id={image_id}. Wrote empty matched/suggested."
        )
    else:
        # 2) Sort candidates by score desc (stronger matches first)
        sorted_candidates = sorted(
            candidate_map.values(), key=lambda x: Decimal(x["score"]), reverse=True
        )

        total_candidates = len(sorted_candidates)
        matched_count = int(math.ceil(total_candidates / 2.0))  # matched gets the extra if odd

        # 3) Build final lists (strip scores before saving)
        matched_tags = [
            {"marriott_tag": c["marriott_tag"], "parent_cat": c["parent_cat"], "aws_label_name": c["label_name"], "score": c["score"]}
            for c in sorted_candidates[:matched_count]
        ]
        suggested_tags = [
            {"marriott_tag": c["marriott_tag"], "parent_cat": c["parent_cat"], "aws_label_name": c["label_name"], "score": c["score"]}
            for c in sorted_candidates[matched_count:]
        ]

        # 4) Persist
        db.table.update_item(
            Key={"PK": f"USER#{user_id}", "SK": f"IMAGE#{image_id}"},
            UpdateExpression="SET matched_tags = :matched, suggested_tags = :suggested",
            ExpressionAttributeValues={
                ":matched": matched_tags,
                ":suggested": suggested_tags,
            },
        )
        logger.info(
            f"Saved {len(matched_tags)} matched and {len(suggested_tags)} suggested tags for image_id={image_id}"
        )

    # === Batch progress & completion (kept from your original) ===
    _handle_batch_progress_sync(user_id, batch_id, db)

    duration = time.perf_counter() - start_time
    logger.info(
        f"Manual tag matching completed: image_id={image_id}, candidates={len(candidate_map)}, matched={matched_count if candidate_map else 0}, duration={duration:.2f}s"
    )

    return {
        "status": "completed",
        "method": "manual",
        "image_id": image_id,
        "batch_id": batch_id,
        "aws_labels_count": len(aws_labels or []),
        "candidates_count": len(candidate_map),
        "matched_tags_count": matched_count if candidate_map else 0,
        "suggested_tags_count": (len(candidate_map) - matched_count) if candidate_map else 0,
        "duration_seconds": duration,
    }


@celery.task(name="images.generate_user_recommendations", **TASK_CONFIG)
def generate_user_recommendations_task(user_id: str) -> dict:
    """
    Generate recommendations for a user by:
      - scanning user's images and collecting matched_tags
      - for each marriott_tag, query TAG#<tag> index to collect content_ids
      - store only content_ids (not full content objects) to avoid DynamoDB 400KB limit
      - omit tags with no content
    """
    start_time = time.perf_counter()
    logger.info(f"Generating user recommendations: user_id={user_id}")

    try:
        from app.core.database import get_db_service
        db = get_db_service()
        table = db.table       

        # 1) Get ALL images for this user across ALL batches
     
        # response = table.scan(
        #     FilterExpression="begins_with(PK, :user_pk) AND begins_with(SK, :image_sk)",
        #     ExpressionAttributeValues={
        #         ":user_pk": f"USER#{user_id}",
        #         ":image_sk": "IMAGE#",
        #     },
        # )
        
        response = table.query(
                    KeyConditionExpression=Key("PK").eq(f"USER#{user_id}") & Key("SK").begins_with("IMAGE#"))
        
        all_images = response.get("Items", [])
        logger.info(f"Found {len(all_images)} total images for user {user_id}")

        # 2) Collect all unique tags by parent category (from matched_tags)
        unique_tags_by_category = {}
        for image in all_images:
            for tag in image.get("matched_tags", []):
                parent_cat = tag.get("parent_cat")
                marriott_tag = tag.get("marriott_tag")
                if not parent_cat or not marriott_tag:
                    continue
                unique_tags_by_category.setdefault(parent_cat, set()).add(marriott_tag)

        # 3) For each tag, query TAG#<tag> -> CONTENT#<content_id> to gather content ids
        # We'll build a mapping: marriott_tag -> set(content_id)
        tag_to_content_ids = {}

        for parent_cat, tags_set in unique_tags_by_category.items():
            for marriott_tag in tags_set:
                content_ids = set()
                try:
                    tag_index_response = table.query(
                        KeyConditionExpression="PK = :pk",
                        ExpressionAttributeValues={":pk": f"TAG#{normalize_token(marriott_tag)}"},
                    )
                    for idx_item in tag_index_response.get("Items", []):
                        cid = idx_item.get("content_id")
                        if not cid:
                            sk = idx_item.get("SK", "")
                            if sk.startswith("CONTENT#"):
                                cid = sk.split("CONTENT#", 1)[1]
                        if cid:
                            content_ids.add(str(cid))
                except Exception as idx_err:
                    logger.warning(f"Failed to query TAG index for {normalize_token(marriott_tag)}: {idx_err}")

                if content_ids:
                    tag_to_content_ids[marriott_tag] = content_ids
                else:
                    logger.debug(f"No content_ids found for tag '{marriott_tag}'")

        # 4) Build recommendation categories with ONLY content_ids (no batch fetch needed)
        recommendation_categories = []
        for parent_cat, tags_set in unique_tags_by_category.items():
            category_data = {
                "parent_cat": parent_cat, 
                "pk": f"MASTER_TAG#{normalize_token(parent_cat)}",  # Store direct match PK for parent category
                "tags": []
            }
            
            for marriott_tag in sorted(tags_set):
                if marriott_tag not in tag_to_content_ids:
                    continue  # no content ids collected for this tag

                content_ids = tag_to_content_ids[marriott_tag]
                if content_ids:
                    # Store only the content_ids list (no full content objects)
                    tag_data = {
                        "marriott_tag": marriott_tag,                    
                        "content": list(content_ids),  # Just store content IDs
                    }
                    category_data["tags"].append(tag_data)
                else:
                    logger.debug(f"Omitting tag '{marriott_tag}' because no content_ids found")

            if category_data["tags"]:
                recommendation_categories.append(category_data)

        # 5) Save recommendation record (much smaller now - only content_ids stored)
        recommendation_id = f"{user_id}"
        created_at = datetime.now(UTC).isoformat()

        recommendation_record = {
            "PK": f"USER#{user_id}",
            "SK": f"RECOMMENDATION#{recommendation_id}",
            "user_id": user_id,
            "recommendation_id": recommendation_id,
            "categories": recommendation_categories,
            "total_images": len(all_images),
            "total_categories": len(recommendation_categories),
            "created_at": created_at,
            "updated_at": created_at,
        }

        table.put_item(Item=recommendation_record)

        duration = time.perf_counter() - start_time
        logger.info(
            f"User recommendations generated: user_id={user_id}, categories={len(recommendation_categories)}, duration={duration:.2f}s"
        )

        return {
            "status": "completed",
            "user_id": user_id,
            "recommendation_id": recommendation_id,
            "total_images": len(all_images),
            "categories_count": len(recommendation_categories),
            "duration_seconds": duration,
        }

    except Exception as e:
        error_msg = f"Failed to generate user recommendations for {user_id}: {str(e)}"
        logger.error(error_msg, exc_info=True)

        # Retry logic
        # if (
        #     generate_user_recommendations_task.request.retries
        #     < generate_user_recommendations_task.max_retries
        # ):
        #     logger.info(
        #         f"Retrying user recommendations: user_id={user_id}, retry={generate_user_recommendations_task.request.retries + 1}"
        #     )
        #     raise generate_user_recommendations_task.retry(
        #         countdown=120 * (2**generate_user_recommendations_task.request.retries)
        #     )

        raise

def normalize_token(s: str) -> str:
    """Normalize a string to a stable token for PK/SK suffix."""
    if s is None:
        return ""
    s0 = str(s).strip().lower()
    s0 = "".join(
        ch for ch in unicodedata.normalize("NFKD", s0) if not unicodedata.combining(ch)
    )

    if not KEEP_AMPERSAND:
        s0 = s0.replace("&", " and ")

    # allow alnum, space, dash, underscore, dot, (ampersand optionally)
    if KEEP_AMPERSAND:
        allowed = r"[^a-z0-9\s\-\_\&\.]"
    else:
        allowed = r"[^a-z0-9\s\-\_\.]"
    s0 = re.sub(allowed, "", s0)

    sep = "_" if USE_UNDERSCORE else "-"
    s0 = re.sub(r"\s+", sep, s0).strip(sep)

    # if we replaced '&' with ' and ', convert to _and_
    if not KEEP_AMPERSAND:
        # convert any leftover ' and ' patterns to _and_ (and collapse separators)
        s0 = re.sub(r"\band\b", "and", s0)
        s0 = (
            s0.replace(" and ", "_and_")
            .replace(" and_", "_and_")
            .replace("_and ", "_and_")
        )
        s0 = re.sub(r"\s+", sep, s0)
        s0 = s0.strip(sep)

    return s0
