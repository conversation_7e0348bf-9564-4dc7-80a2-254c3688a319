"""Configuration management for FastAPI microservices."""

import json
import os
from typing import Any

import boto3
from botocore.exceptions import ClientError, NoCredentialsError, PartialCredentialsError
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


def resolve_secret_name() -> str:
    """Map ENV to correct AWS Secrets Manager path."""
    env = os.environ.get("ENV", "dev").lower()
    valid_envs = {"dev", "prod"}
    if env not in valid_envs:
        raise ValueError(f"Invalid ENV value: {env}")
    return f"{env}/marriot/secret"


def get_aws_secrets(secret_name: str, region_name: str = "us-east-1") -> dict:
    """Fetch secrets from AWS Secrets Manager using IAM role ."""
    try:
        client = boto3.client("secretsmanager", region_name=region_name)
        response = client.get_secret_value(SecretId=secret_name)
        return json.loads(response.get("SecretString", "{}"))
    except ClientError as e:
        raise RuntimeError(f"AWS client error: {e.response['Error']['Message']}")
    except Exception as e:
        raise RuntimeError(f"Unexpected error: {str(e)}")


class Settings(BaseSettings):
    """Main application settings."""

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    # Basic App Settings
    app_name: str = Field(
        default="Fora-Marriott Microservice", description="Application name"
    )
    version: str = Field(default="1.0.0", description="Application version")
    description: str = Field(
        default="Fora-Marriott pseudo-microservice architecture",
        description="Application description",
    )
    debug: bool = Field(default=False, description="Debug mode")
    environment: str = Field(default="development", description="Environment")

    # Security & JWT Configuration
    secret_key: str = Field(
        default="your-super-secret-key-change-in-production",
        description="Secret key for JWT tokens",
    )
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_expire_minutes: int = Field(
        default=1440, description="JWT expiration minutes (24 hours)"
    )
    jwt_refresh_expire_days: int = Field(
        default=30, description="JWT refresh token expiration days"
    )

    # Service Configuration
    host: str = Field(default="0.0.0.0", description="Host to bind to")

    # Service Ports
    auth_service_port: int = Field(default=8010, description="Auth service port")
    images_service_port: int = Field(default=8011, description="Images service port")
    tags_service_port: int = Field(default=8012, description="Tags service port")
    admin_service_port: int = Field(default=8013, description="Admin service port")

    # CORS Settings
    cors_origins: list[str] = Field(default=["*"], description="CORS origins")
    cors_allow_credentials: bool = Field(
        default=True, description="Allow credentials in CORS"
    )
    cors_allow_methods: list[str] = Field(default=["*"], description="CORS methods")
    cors_allow_headers: list[str] = Field(default=["*"], description="CORS headers")

    # AWS Configuration (LocalStack compatible)
    aws_access_key_id: str = Field(default="test", description="AWS access key")
    aws_secret_access_key: str = Field(default="test", description="AWS secret key")
    aws_region: str = Field(default="us-east-1", description="AWS region")

    # S3 Configuration
    s3_bucket_name: str = Field(
        default="fora-marriott-dev-media", description="S3 bucket name"
    )
    s3_endpoint_url: str | None = Field(default=None, description="S3 endpoint URL")
    s3_mock_mode: bool = Field(
        default=True, description="Enable S3 mock mode for testing"
    )
    s3_access_key_id: str = Field(default="", description="S3 access key")
    s3_secret_access_key: str = Field(default="", description="S3 secret key")

    # DynamoDB Configuration
    dynamodb_table_prefix: str = Field(
        default="Fora-Marriott", description="DynamoDB table prefix"
    )
    dynamodb_endpoint_url: str | None = Field(
        default=None, description="DynamoDB endpoint URL"
    )
    dynamodb_table_name: str = Field(
        default="fora-marriott-passion-dev", description="Main DynamoDB table name"
    )
    dynamodb_access_key_id: str = Field(
        default="test", description="DynamoDB access key"
    )
    dynamodb_access_key_id_local: str = Field(
        default="test", description="DynamoDB access key for local"
    )
    dynamodb_secret_access_key: str = Field(
        default="test", description="DynamoDB secret key"
    )
    dynamodb_secret_access_key_local: str = Field(
        default="test", description="DynamoDB secret key for local"
    )

    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379", description="Redis URL")

    # Redis Configuration
    redis_backend_url: str = Field(
        default="redis://localhost:6379/1", description="Redis URL"
    )

    redis_host: str = Field(default="localhost", description="Redis Host")
    redis_port: int = Field(default=6379, description="Redis Port")
    redis_password: str = Field(default="", description="Redis Password")
    redis_ssl: bool = Field(default=False, description="Enable SSL for Redis")

    # Image URL configuration
    image_base_url: str = Field(default="https://s3.amazonaws.com/your-bucket", description="Base URL for all images")

    
    # AWS Rekognition Configuration
    rekognition_confidence_threshold: float = Field(
        default=70.0, description="Minimum confidence threshold for Rekognition labels"
    )

    # Sentry Configuration
    sentry_dsn: str | None = Field(
        default=None, description="Sentry DSN for error tracking"
    )
    sentry_environment: str = Field(
        default="development", description="Sentry environment"
    )
    sentry_traces_sample_rate: float = Field(
        default=0.1, description="Sentry traces sample rate"
    )
    sentry_enabled: bool = Field(
        default=False, description="Enable Sentry error tracking"
    )

    @field_validator(
        "cors_origins", "cors_allow_methods", "cors_allow_headers", mode="before"
    )
    @classmethod
    def parse_cors_list(cls, v: Any) -> list[str]:
        # If already a list, just return
        if isinstance(v, list):
            return [str(i) for i in v]

        # If JSON array string like '["*"]' or '["http://a","http://b"]'
        if isinstance(v, str):
            v = v.strip()
            try:
                parsed = json.loads(v)
                if isinstance(parsed, list):
                    return [str(i) for i in parsed]
            except json.JSONDecodeError:
                # not a JSON array, fall back to comma-split
                pass

            # comma-separated values: "http://a, http://b"
            if "," in v:
                return [i.strip() for i in v.split(",") if i.strip()]

            # single string value
            if v:
                return [v]

        # anything else -> coerce to string and return as single element
        return [str(v)]

    @field_validator("s3_endpoint_url", "dynamodb_endpoint_url", mode="before")
    @classmethod
    def set_localstack_endpoints(cls, v: Any, info) -> str | None:
        """Set LocalStack endpoints if use_localstack is True."""
        if v is None and info.data.get("use_localstack", True):
            return info.data.get("localstack_endpoint", "http://localhost:4566")
        return v


# Global settings instance
_settings: Settings | None = None


# def get_settings() -> Settings:
#     """Get the global settings instance."""
#     global _settings
#     if _settings is None:
#         _settings = Settings()
#     return _settings

def get_settings() -> Settings:
    """Load settings from AWS Secrets Manager or fallback to .env."""
    global _settings
    if _settings is None:
        try:
            secret_name = resolve_secret_name()
            print (f"Secret name: {secret_name}")
            secrets = get_aws_secrets(secret_name=secret_name)
            secrets = {k.lower(): v for k, v in secrets.items()}
            print(f"Secrets : {secrets}")
            _settings = Settings(**secrets)
        except Exception as e:
            print(f"⚠️ Failed to load secrets from AWS: {e}")
            _settings = Settings()  # fallback to .env
    return _settings
