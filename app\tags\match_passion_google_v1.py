#!/usr/bin/env python3
"""
Simple local test for the LLM-based Passion Matcher.

Requirements:
  pip install google-generativeai
"""

import hashlib
import json
import os
import random
import threading
import time
from collections import OrderedDict
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

# ==== CONFIGURATION ====
API_KEY = "xxx"   # <-- Put your real Gemini key here
MODEL_NAME = "models/gemini-2.0-flash" # You can change to gemini-1.5-pro or others
TOP_K = 3

# Example AWS Rekognition-style labels for a beach image
AWS_LABELS =[
                    {
                        "score": "0.9999017333984375",
                        "label": "Bag"
                    },
                    {
                        "score": "0.9978693389892578",
                        "label": "Accessories"
                    },
                    {
                        "score": "0.9978693389892578",
                        "label": "Handbag"
                    },
                    {
                        "score": "0.9974147033691406",
                        "label": "Clothing"
                    },
                    {
                        "score": "0.9974147033691406",
                        "label": "Footwear"
                    },
                    {
                        "score": "0.9974147033691406",
                        "label": "Shoe"
                    },
                    {
                        "score": "0.9957437133789062",
                        "label": "Backpack"
                    },
                    {
                        "score": "0.9944367218017578",
                        "label": "Backpacking"
                    },
                    {
                        "score": "0.9479109954833984",
                        "label": "Hat"
                    },
                    {
                        "score": "0.9022752380371094",
                        "label": "Photography"
                    },
                    {
                        "score": "0.8934603118896485",
                        "label": "Camera"
                    },
                    {
                        "score": "0.8934603118896485",
                        "label": "Electronics"
                    },
                    {
                        "score": "0.8562489318847656",
                        "label": "Outdoors"
                    },
                    {
                        "score": "0.808559799194336",
                        "label": "Adventure"
                    },
                    {
                        "score": "0.808559799194336",
                        "label": "Hiking"
                    },
                    {
                        "score": "0.808559799194336",
                        "label": "Leisure Activities"
                    },
                    {
                        "score": "0.808559799194336",
                        "label": "Nature"
                    }
                ]



# =======================

try:
    import google as genai
except Exception:
    raise RuntimeError("You must install google-generativeai: pip install google-generativeai")


PASSIONS_TEXT = """
OUTDOOR: Hike & Glamp, Wildlife, Safari, Ski & Snowboard, Bike, Trail Running, Rock Climbing, Fish, Scuba & Snorkel, Kayak & Canoe, Surf, Paddleboarding
LIFESTYLE: Golf, Tennis, Running, Swimming, Water Sports, Beach Retreat, City Exploration, Sailing & Boating, Horse Riding, Waterfront, Mountains, Adventure Sports
CULINARY: Restaurants, Foodie Trends, Fine Dining, Local Cuisine, Cooking & Baking, Wine, Beer, Spirits, Coffee, Mixology
WELLNESS: Cardio, Strength Training, Yoga, Medical, Mindfulness, Healthy Food, Self-care & Spa, Home & Garden, Sleep, Longevity
LIFE ENRICHMENT: Kids & Grandkids, Friends, Pets, Business & Finance, Skill Building, Professional Development, Emerging Tech, Environment, Community Care
ENTERTAINMENT: Football, Formula 1, Soccer, Tennis, Golf, Basketball, Baseball, Winter Sports, Music, Artist Fandom, Festivals & Concerts, Show Fandom, Movie Genres, Hollywood Culture, Actor Fandom, Gambling, Video Games, Theme Park
ARTS & CULTURE: Museums, Historical Events, Politics & Current Events, Landmarks, Art & Photography, Theater, Architecture, Literature, Creation, STEM, Fashion, Beauty, Shopping, Local Culture, Painting & Drawing, Sculpting, Crafts, Classic Cars, Antiques & Vintage
""".strip()

# ---- Simple cache ----
class LruTtlCache:
    def __init__(self, capacity=1000, ttl_seconds=3600):
        self.capacity = capacity
        self.ttl = ttl_seconds
        self.store: OrderedDict[str, Tuple[float, Any]] = OrderedDict()
        self.lock = threading.Lock()
    def get(self, key):
        with self.lock:
            item = self.store.get(key)
            if not item:
                return None
            ts, val = item
            if (time.time() - ts) > self.ttl:
                self.store.pop(key, None)
                return None
            self.store.move_to_end(key)
            return val
    def set(self, key, val):
        with self.lock:
            self.store[key] = (time.time(), val)
            self.store.move_to_end(key)
            if len(self.store) > self.capacity:
                self.store.popitem(last=False)

def stable_cache_key(labels: List[Dict[str, Any]], top_k: int) -> str:
    sorted_labels = sorted(
        [(str(l.get("label")), float(l.get("score", 0.0))) for l in labels],
        key=lambda x: (-x[1], x[0])
    )[:15]
    payload = json.dumps({"labels": sorted_labels, "k": top_k}, separators=(",", ":"), sort_keys=True)
    import hashlib
    return hashlib.md5(payload.encode("utf-8")).hexdigest()

@dataclass
class MatcherConfig:
    api_key: str
    model_name: str = MODEL_NAME
    temperature: float = 0.1
    top_p: float = 0.8
    max_output_tokens: int = 1024
    cache_capacity: int = 2000
    cache_ttl_seconds: int = 3600
    max_retries: int = 4
    timeout_seconds: int = 20

def build_prompt(aws_labels: List[Dict[str, Any]], top_k: int) -> str:
    labels_text = "\n".join([f"- {l['label']} (confidence: {float(l['score']):.2f})" for l in aws_labels])
    
    return f"""You are a travel passion matching expert. Given AWS Rekognition labels from a user's photo, match them to relevant travel passions.

AVAILABLE PASSIONS:
{PASSIONS_TEXT}

DETECTED LABELS FROM PHOTO:
{labels_text}

MATCHING RULES:
1. Prioritize specific activities over generic categories
2. Consider context (e.g., "child + indoor + portrait" → Kids & Grandkids)
3. Beach/water signals → Beach, Surf, Water Sports
4. Food/restaurant → Culinary passions
5. Architecture/landmarks → Arts & Culture
6. Sports equipment → Specific sport passion
7. Family/baby → Life Enrichment

Important contextual expectations:
- Beach + Person does NOT imply "Water Sports" unless labels like "Surf", "Surfboard", "Kayak", or "Canoe" appear.
- "Piano" without "Stage" maps to "Music", not "Theater".
- "Horse" + "Person" and no "Wildlife" implies "Horseback Riding" (if not present in the catalog, you may still suggest it).
- "Child" + "Family" ⇒ "Kids & Grandkids" when available.
- Woods/Forest with a person likely indicates "Hike"; with "Camera/Tripod" it leans "Art & Photography".
- A generic "Person" alone does not imply fitness.
- Do NOT match “Ski & Snowboard” from generic “Snow/Mountain” alone. Require explicit cues like “Ski”, “Skiing”, “Snowboard”, or visible gear. 
If only “Snow/Mountain”, treat it as a low-confidence suggestion (≤0.59).
- Do NOT match “Wildlife” unless labels include an animal (e.g., “Deer”, “Bird”, “Bear”, “Horse (wild)”, etc.). 
“Woods + Camera/Tripod” without animals ⇒ “Art & Photography” (and possibly “Hike”), not “Wildlife”.
- Prefer “Hike & Glamp” over plain “Hike” when “Tent” appears with “Mountain/Backpack”.
- When multiple top items are very similar, prefer diversity across parents unless the evidence is overwhelmingly for one parent.
-Always include at least one item in "matched" if any reasonable candidate exists. If you would otherwise return only suggestions, promote the single best candidate into "matched".
-If the promoted item lacks explicit activity cues, cap its score at ≤ 0.59 and keep the reason concise.
-Only return empty matched and suggested when there is truly no reasonable candidate.
-Never invent activities not implied by labels; prefer broad, low-confidence passions (e.g., “Art & Photography”, “Hike”) over specific sports when evidence is weak.
    
try to find realstic or near match and Return max {top_k} matches and max {top_k} suggestions in pure JSON:
{{
  "matched": [{{"tag": "Passion", "category": "Category", "score": 0.9, "reasoning": "why"}}],
  "suggested": [{{"tag": "Passion", "category": "Category", "score": 0.7, "reasoning": "why"}}]
}}
Consider always have less label in suggested than matched. 
Score 0.0–1.0. ≥0.80 = strong, 0.60–0.79 = good, ≤0.59 = weak.
Prefer precision over recall.
"""

def parse_llm_response(text: str) -> Dict[str, Any]:
    text = text.strip()
    if text.startswith("```"):
        text = text.split("```")[1].strip()
        if text.startswith("json"):
            text = text[4:].strip()
    try:
        data = json.loads(text)
    except Exception:
        return {"matched": [], "suggested": [], "error": "Invalid JSON"}
    return data

class LLMPassionMatcher:
    def __init__(self, config: MatcherConfig):
        self.cfg = config
        self.cache = LruTtlCache(config.cache_capacity, config.cache_ttl_seconds)
        genai.configure(api_key=config.api_key)
        self.model = genai.GenerativeModel(config.model_name)
    def match(self, labels, top_k=6):
        key = stable_cache_key(labels, top_k)
        if cached := self.cache.get(key):
            return {"cached": True, **cached}
        prompt = build_prompt(labels, top_k)
        for attempt in range(self.cfg.max_retries):
            try:
                resp = self.model.generate_content(
                    prompt,
                    generation_config={
                        "temperature": self.cfg.temperature,
                        "top_p": self.cfg.top_p,
                        "max_output_tokens": self.cfg.max_output_tokens,
                        
                    },
                )
                result = parse_llm_response(resp.text)
                self.cache.set(key, result)
                return {"cached": False, **result}
            except Exception as e:
                print(f"Retry {attempt+1}: {e}")
                time.sleep(min(2**attempt, 8) + random.random())
        return {"matched": [], "suggested": [], "error": "LLM request failed"}

if __name__ == "__main__":
    cfg = MatcherConfig(api_key=API_KEY)
    matcher = LLMPassionMatcher(cfg)
    start_time = time.time()

    print("Running Passion Matcher test...\n")
    result = matcher.match(AWS_LABELS, TOP_K)
    print(json.dumps(result, indent=2))
    
    end_time = time.time()
    elapsed = end_time - start_time
    print(f"\nTime taken: {elapsed:.2f} seconds")

